import { NextLink } from '../../../components/common/NextLink'
import { cn } from '../../../helper-functions/UtilFunctions'
import type { SelectOptionProps } from '../../../types/components/Select.types'
import { BoxContainer } from '../../common/BoxContainer'
import { GenericButton } from '../../common/GenericButton'

/** `SelectOption` - A component that renders a selectable option within a list.
 * It uses a button to display the option's label and triggers a selection handler when clicked.
 */
export const SelectOption = <T,>({
  option,
  handleSelect,
  buttonProps,
  ...rest
}: SelectOptionProps<T>) => {
  return (
    <BoxContainer as='li' {...rest}>
      {option?.href ? (
        <NextLink
          href={option?.href}
          skipLocalization
          objectId='language_select'
          customValue={option?.value}
          className={cn(
            'button relative w-full cursor-pointer select-none items-start justify-between rounded-sm px-3.5 py-3 text-left hover:bg-gray-100',
            buttonProps?.className
          )}
        >
          {option?.label}
        </NextLink>
      ) : (
        <GenericButton
          type='button'
          onClick={() => handleSelect?.(option)}
          className={cn(
            'relative w-full cursor-pointer select-none items-start justify-between rounded-sm px-3.5 py-3 text-left hover:bg-gray-100',
            buttonProps?.className
          )}
        >
          {option?.label}
        </GenericButton>
      )}
    </BoxContainer>
  )
}
