import strings from '../../data-resource/strings.json'
import { cn } from '../../helper-functions/UtilFunctions'
import type { LinkWithImageProps } from '../../types/components/NextLink.types'
import { BoxContainer } from './BoxContainer'
import { SanityImage } from './SanityImage'

/** `ComingSoonImage` is a component that displays an image wrapped within a
 * "Coming Soon" badge. It uses the `LinkImage` component to render the image
 * and overlays a styled badge if the image is marked as coming soon.
 */
export const ComingSoonImageLink = ({
  linkImage,
  imageProps,
  comingSoonProps,
  ...rest
}: Omit<LinkWithImageProps, 'href' | 'parentSectionId'>) => {
  return (
    <BoxContainer {...rest} className={cn('relative w-fit', rest?.className)}>
      <BoxContainer {...comingSoonProps}>{strings.COMING_SOON}</BoxContainer>
      <SanityImage
        src={linkImage.url}
        alt={linkImage.altText}
        fillProp
        {...imageProps}
        skeletonProps={{
          className: cn(
            'z-100 h-12 w-36 opacity-70',
            imageProps?.skeletonProps?.className
          ),
        }}
        sizes={'9rem'}
      />
    </BoxContainer>
  )
}
