'use client'

import { type ReactNode, useEffect, useState } from 'react'
import { CONSTANTS } from '../data-resource/constants'
import type { LanguagesType } from '../types/common.types'
import { LanguageContext } from './LanguageContext'

/**  LanguageProvider is responsible for managing the language.
 * It sets the language by default to the first language in the CONSTANTS.LANGUAGES array
 * and updates the state if the initialLang prop is provided.
 *
 * The provider also provides a setLanguage function that can be used to update
 * the language and update the state.
 *
 * Additionally, the provider sets the language to the value stored in the initialLang
 * prop if the prop exists.
 */
export function LanguageProvider({
  children,
  initialLang,
}: {
  children: ReactNode
  initialLang?: LanguagesType
}) {
  const [language, setLanguage] = useState<LanguagesType>(
    initialLang ?? CONSTANTS.LANGUAGES[0]
  )
  const [isLoaded, setIsLoaded] = useState(Boolean(initialLang))

  useEffect(() => {
    if (initialLang) {
      setLanguage(initialLang)
      setIsLoaded(true)
    }
  }, [initialLang])

  return (
    <LanguageContext.Provider value={{ language, setLanguage, isLoaded }}>
      {children}
    </LanguageContext.Provider>
  )
}
