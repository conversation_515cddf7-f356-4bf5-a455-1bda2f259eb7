'use client'

import { useState } from 'react'

import { NextLink } from '../../components/common/NextLink'
import { SanityImage } from '../../components/common/SanityImage'
import { LanguageSelect } from '../../components/ui/select/LanguageSelect'
import { WebsiteIdSelect } from '../../components/ui/select/WebsiteIdSelect'
import { Skeleton } from '../../components/ui/skeleton/Skeleton'
import { UI_TEST_ID } from '../../data-resource/data-test-ids'
import { cleanPageDomain, cn } from '../../helper-functions/UtilFunctions'
import { useBreakpointValue } from '../../hooks/useBreakpointValue'
import { useTrackPageView } from '../../hooks/useTrackPageView'
import { useTracking } from '../../hooks/useTracking'
import { useLanguage } from '../../providers/LanguageContext'
import { useWebsiteId } from '../../providers/WebsiteIdContext'
import { isProd } from '../../serverless/keys'
import { STYLE } from '../../styles/style'
import { NavEvent } from '../../types/Analytics/AnalyticsEvents.types'
import type { NavigationMenuDataProps } from '../../types/shared-page-data.types'
import { DesktopNav } from './DesktopNav'
import { MobileNav } from './MobileNav'

import dynamic from 'next/dynamic'
import { usePathname } from 'next/navigation'
import { HideContent } from '../../components/common/HideContent'

const AuthorizationUI = dynamic(
  () => import('./AuthorizationUI').then((mod) => mod.AuthorizationUI),
  {
    ssr: false,
  }
)

/** Navigation menu component, this component is used to render the navigation menu on page.
 * It contains desktop version of navigation menu in DesktopNav and mobile version MobileNav
 * The logo always redirects to the root directory ("/"), and does not retrieve data from Sanity.
 */
export function NavigationMenu({
  sharedPageData,
  feedbackData,
}: NavigationMenuDataProps) {
  useTracking()
  useTrackPageView()

  const pathname = usePathname()
  const { isLoaded } = useWebsiteId()
  const [isOpen, setIsOpen] = useState(false)
  const { websiteId, setWebsiteId, isLoaded: websiteIdLoaded } = useWebsiteId()
  // const { language, setLanguage, isLoaded: languageLoaded } = useLanguage()
  const { language, setLanguage } = useLanguage()

  const websiteTitles =
    sharedPageData?.websiteIds?.map((website) => ({
      ...website,
      cleanedTitle: cleanPageDomain(website?.title),
    })) ?? []

  const onToggle = () => setIsOpen(!isOpen)

  const { isBreakpoint: isDesktop, isLoading } = useBreakpointValue([
    'xl',
    'xxl',
  ])
  const navigationMenuData = sharedPageData?.navigationMenu

  return (
    <HideContent>
      <Skeleton
        loading={!isLoaded || isLoading}
        data-cy={UI_TEST_ID?.navBar}
        as='nav'
        role='navigation'
        className={cn(
          'sticky top-0 flex h-(--nav-height) items-center justify-between overflow-visible rounded-none bg-background-100 px-6 shadow-md xl:gap-4',
          STYLE.Z_INDEX.NAV_BAR,
          !navigationMenuData?.navigationItems && 'justify-center'
        )}
      >
        <NextLink
          href={'/'}
          customEvent={NavEvent.item_clicked}
          customHoverEvent={NavEvent.item_hovered}
          objectId='nav_bar_main_link'
          trackHover
          className={cn(
            navigationMenuData?.languagePickerVisibility
              ? 'xxl:mr-10'
              : 'xxl:mr-20'
          )}
        >
          {navigationMenuData?.websiteLogo && (
            <SanityImage
              fillProp
              skeletonProps={{ className: 'w-35 h-10' }}
              alt={navigationMenuData?.websiteLogo?.altText}
              src={navigationMenuData?.websiteLogo?.url}
            />
          )}
        </NextLink>
        {isDesktop && (
          <DesktopNav
            languagePickerVisible={
              !navigationMenuData?.languagePickerVisibility
            }
            navigationMenuData={navigationMenuData}
          />
        )}
        {!isProd && websiteIdLoaded && (
          <WebsiteIdSelect
            websiteId={websiteId}
            websiteTitles={websiteTitles}
            setWebsiteId={setWebsiteId}
          />
        )}
        {/* {!isProd &&
          !navigationMenuData?.languagePickerVisibility &&
          languageLoaded && ( */}
        <LanguageSelect
          pathname={pathname}
          language={language}
          setLanguage={setLanguage}
        />
        {/* )} */}
        {isDesktop && !navigationMenuData?.hideAuthButtons && (
          <AuthorizationUI
            loginLink={navigationMenuData?.loginLink}
            registerLink={navigationMenuData?.registerLink}
          />
        )}
        {!isDesktop && (
          <MobileNav
            navigationMenuData={navigationMenuData}
            feedbackData={feedbackData}
            inProp={isOpen}
            onToggle={onToggle}
          />
        )}
      </Skeleton>
    </HideContent>
  )
}
