import { cookies, draftMode } from 'next/headers'
import { notFound } from 'next/navigation'

import { DynamicPageContent } from '../../../components/page-content/DynamicPageContent'
import { PageContent } from '../../../components/page-content/PageContent'
import { CONSTANTS } from '../../../data-resource/constants'
import { processPages } from '../../../helper-functions/AppUtilFunctions'
import { getWebsiteId } from '../../../helper-functions/UtilFunctions'
import {
  fetchContentPageData,
  fetchPageSEOData,
  fetchSanityData,
} from '../../../sanity-queries/query-fetch-function'
import {
  pageDataQuery,
  pagesDataQuery,
  shareSectionQuery,
} from '../../../sanity-queries/sanity-queries'
import { logServerless } from '../../../serverless/ApiUtilFunctions'
import type {
  PagesData,
  StaticPageParamData,
  TParams,
} from '../../../types/pages.types'
import type { SharedSectionDataType } from '../../../types/sections/section.types'

export const dynamicParams = false // Replacement for the fallback option | false = 404}

export async function generateStaticParams() {
  const pages = await fetchSanityData<StaticPageParamData>({
    query: pagesDataQuery,
  })

  // Generate params for each language and slug combination
  const slugs = processPages(pages)

  return slugs.flatMap((slug) =>
    CONSTANTS.LANGUAGES.map((lang) => ({
      lang,
      slug: slug?.slug,
    }))
  )
}

export async function generateMetadata({ params }: { params: TParams }) {
  const { slug, lang } = await params

  try {
    const { isEnabled } = await draftMode()
    const seoData = await fetchPageSEOData({
      slug,
      preview: isEnabled,
      lang,
    })
    return seoData
  } catch (error) {
    logServerless({
      message: `SEO data for page ${slug?.[0]} not found (${lang})`,
      logLevel: 'error',
      error,
    })
    return {
      title: 'Not Found',
      description: 'The page you are looking for does not exist',
    }
  }
}

/**
 * Page component that renders page data dynamically from Sanity CMS
 * with language support
 */
async function Page({ params }: { params: TParams }) {
  const { isEnabled } = await draftMode()
  const { slug: slugArray } = await params
  const paramSlug = slugArray?.[0] ?? ''

  const websiteId = await getWebsiteId(cookies)

  // Sub Content Pages
  if (slugArray?.length > 1) {
    const { isFeatured, moreLikeThis, pageData, pageDomain } =
      await fetchContentPageData({
        slug: slugArray,
      })

    const shareSectionData = await fetchSanityData<SharedSectionDataType>({
      query: shareSectionQuery,
      websiteIdCookie: websiteId,
    })

    return (
      <DynamicPageContent
        moreLikeThis={moreLikeThis}
        parentSlug={paramSlug}
        singleItemData={pageData}
        shareSectionData={shareSectionData}
        isFeatured={isFeatured}
        pageDomain={pageDomain}
      />
    )
  }

  const pageData = await fetchSanityData<PagesData>({
    query: pageDataQuery,
    params: {
      slug: paramSlug,
    },
    websiteIdCookie: websiteId,
  })

  if (!pageData && !isEnabled) {
    notFound()
  }

  // Normal Content Pages
  return (
    <PageContent
      pageDomain={pageData?.pageDomain}
      pageData={pageData?.pageSections}
      pageSlug={paramSlug}
    />
  )
}

export default Page
