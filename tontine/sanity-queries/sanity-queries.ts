import {
  sectionMediaQuery,
  seoImageQuery,
  sharedSectionDataQuery,
} from './common-type-queries/common-types-queries'
import { contentPostQuery } from './content-post-queries/content-post-queries'
import {
  feedbackQuery,
  pagesQuery,
} from './miscellaneous-queries/miscellaneous-queries'
import { footerQueryOnWebsite } from './nav-menu-footer-queries/footer-query'
import { navigationMenuOnWebsiteQuery } from './nav-menu-footer-queries/navigation-menu-query'
import { aboutUsSectionQuery } from './section-queries/about-us-section-query'
import { carouselHeroSectionQuery } from './section-queries/carousel-hero-section-query'
import { carouselSectionQuery } from './section-queries/carousel-section-query'
import { companiesLogosSectionQuery } from './section-queries/companies-logo-section-query'
import { contactUsQuery } from './section-queries/contact-us-section-query'
import { contentSectionQuery } from './section-queries/content-section-query'
import { ctaCardSectionQuery } from './section-queries/cta-card-section-query'
import { ctaSectionQuery } from './section-queries/cta-section-query'
import { downloadSectionQuery } from './section-queries/download-section-query'
import { faqSectionQuery } from './section-queries/faq-section-query'
import { featuredSectionQuery } from './section-queries/featured-section-query'
import { glossarySectionQuery } from './section-queries/glossary-section-query'
import { heroBannerSection } from './section-queries/hero-banner-section-query'
import { infoBannerSectionQuery } from './section-queries/info-banner-section-query'
import { infoBlockSectionQuery } from './section-queries/info-block-section-query'
import { infoHubSectionQuery } from './section-queries/info-hub-section-query'
import { markdownSectionQuery } from './section-queries/markdown-section-query'
import {
  ReferralSectionQuery,
  TontinatorSectionQuery,
} from './section-queries/mtl-section-queries'
import { partnerSectionQuery } from './section-queries/partner-section-query'
import { shareSectionQuery } from './section-queries/share-section-query'
import { subscribeQuery } from './section-queries/subscribe-section-query'
import { teamQuery } from './section-queries/team-section-query'
import { testimonialSectionQuery } from './section-queries/testimonials-section-query'
import { videosSectionQuery } from './section-queries/videos-section-query'
import { walkthroughSectionQuery } from './section-queries/walkthrough-section-query'
import { corporationSchemaQuery } from './web-schema-queries/corporation-query'
import { investmentOrDepositSchemaQuery } from './web-schema-queries/investment-or-deposit-query'

const pagesDataQuery: string = /* groq */ `
*[_id == $WEBSITE_TONTINE][0]{
  pagesOnWebsite[]->{
    ${pagesQuery}
  },
}
`

const imagesQuery = `
"images": array::unique(
  array::compact(
    pageSections[]->{
      "combinedImages": [
        sectionImage.asset->url,
        videoThumbnail.asset->url,
        icon.asset->url,
        ...infoHubCardList[]->.icon.asset->url,
        ...testimonialPosts[].testimonialReviewerImage.asset->url,
        ...carouseHeroSectionPosts[]->.sectionImage.asset->url,
        ...carouselPosts[].carouselPostReviewerImage.asset->url,
        ...partnersCompanies[]->.partnerImage.asset->url,
        ...companiesLogosIcon[]->.imageField.asset->url,
        ...teamMembers[]->.personImage.asset->url,
        ...teamMembers[]->.personAlternativeImage.asset->url,
        ...featuredPost[]->.postImage.asset->url,
        ...postsArray[]->.postImage.asset->url,
      ]
    }.combinedImages[]
  )
)
`

const videosQuery = `
"videos": array::compact(
  pageSections[]->{
    ...sectionVideo{
      ...asset->{
        defined(playbackId) => {"playbackId": playbackId},
        defined(data.duration) => {"duration": data.duration},
        "title": filename
      },
      "thumbnail_loc": coalesce(^.videoThumbnail, ^.sectionImage).asset->url,
      "description": coalesce(pt::text(^.localizedTitle.en), ^.stringTitle.en)
    }
  }
)[defined(@.playbackId)]  // 👈 Key filter added here
`

const sitemapContentQuery = /* groq */ `
"updatedAt": _updatedAt,
"pageSlug": coalesce(^.pageSlug.current + "/" + coalesce(slug.current, ""), slug.current + "/"),
"images": [postImage.asset->url],
defined(manuscript) => { 
  manuscript {
    asset->{
      originalFilename,
      "updatedAt": _updatedAt,

    },
  },
},
`

const sitemapVideoQuery = `
"videos": array::compact([videoFile{
  ...asset->{
    defined(playbackId)=>{"playbackId": playbackId},
    defined(data.duration)=>{"duration": data.duration},
    "title": filename,
  },
  "thumbnail_loc": coalesce(^.thumbnail, ^.postImage).asset->url,
  "description": coalesce(pt::text(^.localizedTitle.en), pt::text(^.localizedSubtitle.en))
}])
`

const siteMapQuery = /* groq */ `
*[_id == $WEBSITE_TONTINE][0]{
  "pagesOnWebsite": array::unique(
    array::compact(
      [
        homepage->{
          "pageSlug": pageSlug.current,
          "updatedAt": _updatedAt,
          ${imagesQuery},
          ${videosQuery},
        },
        ...pagesOnWebsite[]->{
          "pageSlug": pageSlug.current,
          "updatedAt": _updatedAt,
          ${imagesQuery},
          ${videosQuery},
        },
        ...pagesOnWebsite[]->{
          "sections": pageSections[@->_type == 'contentSection']->postsArray[]->{
          ${sitemapContentQuery}
          ${sitemapVideoQuery}
          }
        }.sections[],
        ...pagesOnWebsite[]->{
          "sections": pageSections[@->_type == 'contentSection']->featuredPost[]->{
            ${sitemapContentQuery}
            ${sitemapVideoQuery}
          }
        }.sections[]
      ]
    )
  )
}.pagesOnWebsite
`

/**
 * The homePageQuery retrieves various data elements for the home page, including SEO fields, sections, header, footer, and any additional information specifically provided for the home page.
 */
const homePageQuery: string = /* groq */ `*[_id == $WEBSITE_TONTINE][0]{
  webschemasOnWebsite[]->{
    ${corporationSchemaQuery}
    ${investmentOrDepositSchemaQuery}
  },
  pageDomain,
  "homepage": homepage->{
    pageSlug{
      current,
    },
    pageSections[]->{
        _type,
        ${sectionMediaQuery}
        ${sharedSectionDataQuery}
        ${carouselHeroSectionQuery}
        ${heroBannerSection}
        ${contactUsQuery}
        ${subscribeQuery}
        ${carouselSectionQuery}
        ${infoBannerSectionQuery}
        ${infoHubSectionQuery}
        ${partnerSectionQuery}
        ${infoBlockSectionQuery}
        ${markdownSectionQuery}
        ${faqSectionQuery}
        ${aboutUsSectionQuery}
        ${ctaSectionQuery}
        ${featuredSectionQuery}
        ${downloadSectionQuery}
        ${TontinatorSectionQuery}
        ${ReferralSectionQuery}
        ${companiesLogosSectionQuery}
        ${testimonialSectionQuery} 
        ${ctaCardSectionQuery}    
        ${walkthroughSectionQuery}  
        ${glossarySectionQuery}
        ${videosSectionQuery}
    },
  },
}
`

const sharedPageDataQuery = /* groq */ `{
  ...*[_id == $WEBSITE_TONTINE]{
    pageDomain,
    siteName,
    twitterCreator,
    twitterSite,
    twitterCardType,
    openGraphType,
  }[0],
  ...*[_type == $pageType && pageSlug.current == $slug][0]{
    "pageSeo": {
      "pageTitle": coalesce(localizedTitle[$lang], localizedTitle.en),
      "seoDescription": coalesce(localizedSubtitle[$lang], localizedSubtitle.en),
      "seoKeywords": coalesce(seoKeywords[$lang], seoKeywords.en),
      ${seoImageQuery},
    },
  }
}
`

const footerAndNavQuery = /* groq */ `*[_id == $WEBSITE_TONTINE][0]{
  pageDomain,
  "navigationMenu": navigationMenu->{
    ${navigationMenuOnWebsiteQuery}
  },
  ${feedbackQuery}
  "footer": footer->{
    ${footerQueryOnWebsite}
  },
  "websiteIds": *[_type == 'website']{"id": _id, "title": pageDomain}
}`

/**
 * The pageSections retrieves data from sections, that can be used in dynamical pages
 */
const pageSectionQueries: string = `
${sharedSectionDataQuery}
${sectionMediaQuery}
${carouselHeroSectionQuery}
${heroBannerSection}
${carouselSectionQuery}
${partnerSectionQuery}
${infoBannerSectionQuery}
${infoBlockSectionQuery}
${infoHubSectionQuery}
${markdownSectionQuery}
${faqSectionQuery}
${aboutUsSectionQuery}
${ctaSectionQuery}
${featuredSectionQuery}
${downloadSectionQuery}
${TontinatorSectionQuery}
${ReferralSectionQuery}
${teamQuery}
${contactUsQuery}
${subscribeQuery}
${companiesLogosSectionQuery}
${testimonialSectionQuery}
${contentSectionQuery}
${ctaCardSectionQuery}
${walkthroughSectionQuery}
${glossarySectionQuery}
${videosSectionQuery}
`

/**
 * Fetches data for a single page based on slug
 */
export const pageDataQuery = /* groq */ `*[_type == $pageType && pageSlug.current == $slug][0]{
  pageSlug,
  pageDomain,
  pageSections[]->{
    ...,
    ${pageSectionQueries}
  },
  ...*[_id == $WEBSITE_TONTINE][0]{
    pageDomain,
  },
}
`

/**
 * Fetches featured post and posts data for single content page based on parent and child slug
 */
const contentPageQuery = /* groq */ `
{
  ...*[_type == $pageType && pageSlug.current == $parentSlug][0]{
    pageSections[]->{
      defined(postsArray) => {
        "posts": postsArray[@->slug.current == $childSlug][0]->{
          ${contentPostQuery}
        },
      },
      defined(featuredPost) => {
        "featuredPost": featuredPost[@->slug.current == $childSlug][0]->{
          ${contentPostQuery}
        },
      },
    },
    "seoKeywords": seoKeywords[$lang],
  },
  ...*[_id == $WEBSITE_TONTINE][0]{
    pageDomain,
  }
}
`

export {
  contentPageQuery,
  footerAndNavQuery,
  homePageQuery,
  pagesDataQuery,
  sharedPageDataQuery,
  shareSectionQuery,
  siteMapQuery,
}
