import type { Rule } from 'sanity'

import type { GeneratedFieldKeys } from '../../types/localizationTypes'
import { validationRules } from '../validation'

type TitleAndDescription = {
  title?: string | boolean
  options?: object
  description?: string
  group?: string
  fieldset?: string
  type?: GeneratedFieldKeys
  isRequired?: boolean
  validation?: (rule: Rule) => Array<Rule> | Rule
}
type HeaderFieldsProps = {
  subtitle?: {
    showSubtitle?: boolean
  } & TitleAndDescription
  icon?: {
    showIcon?: boolean
  } & TitleAndDescription
} & TitleAndDescription

/** Function that returns a schema for a section header based on the provided fields.
 * If the subtitle or icon properties are not provided, or if their respective
 * `showSubtitle` or `showIcon` properties are not truthy, the corresponding
 * fields will not be included in the schema.
 */
export function headerSchemaFields({
  title = 'Section title',
  description,
  type,
  group,
  fieldset,
  subtitle,
  icon,
  options,
  isRequired = true,
  validation,
}: HeaderFieldsProps) {
  return [
    title && {
      name: type?.includes('string') ? 'stringTitle' : 'localizedTitle',
      title,
      type: type ?? 'richTextTitleLite',
      group,
      fieldset,
      options,
      description: description ?? 'Localized title representing the section.',
      validation: isRequired ? validationRules.isRequired : validation,
    },

    subtitle?.showSubtitle && {
      name: subtitle?.type?.includes('string')
        ? 'stringSubtitle'
        : 'localizedSubtitle',
      title: subtitle?.title ?? 'Section subtitle',
      type: subtitle?.type ?? 'richTextSubtitle',
      group: subtitle?.group,
      fieldset: subtitle?.fieldset,
      options: subtitle?.options,
      description:
        subtitle?.description ??
        'Localized descriptive subtitle for the section.',
      validation: subtitle?.isRequired
        ? validationRules.isRequired
        : subtitle?.validation,
    },

    icon?.showIcon && {
      name: 'icon',
      title: icon?.title ?? 'Section icon',
      type: icon?.type ?? 'image',
      group: icon?.group,
      fieldset: icon?.fieldset,
      validation: icon?.validation,
      description: icon?.description ?? 'An icon that represents the section.',
      options: {
        ...icon?.options,
        hotspot: true,
      },
    },
  ].filter(Boolean)
}

export const internalUseTitle = {
  name: 'title',
  title: 'Internal Title',
  type: 'string',
  description: 'Title used internally for sanity (not visible to end users).',
}
