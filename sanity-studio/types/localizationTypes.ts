import type { fieldsConfig } from '../schemas/common/localeFields'

type FieldName = (typeof fieldsConfig)[number]['name']

type Capitalize<T extends string> = T extends `${infer First}${infer Rest}`
  ? `${Uppercase<First>}${Rest}`
  : T

export type GeneratedFieldKeys =
  | `string${Capitalize<FieldName>}`
  | `sr${Capitalize<FieldName>}`
  | `richText${Capitalize<FieldName>}`
  | `richText${Capitalize<FieldName>}Lite`
  | `rtr${Capitalize<FieldName>}`
  | `rtr${Capitalize<FieldName>}Lite`
